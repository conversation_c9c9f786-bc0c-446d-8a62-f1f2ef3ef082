import pytest
from django.urls import reverse
from rest_framework.test import APITestCase
from authentication.models import <PERSON><PERSON>, Driver, OTP
from unittest.mock import patch

pytestmark = pytest.mark.django_db

class AuthenticationTests(APITestCase):
    def test_user_registration(self):
        url = reverse('register')
        data = {
            "email": "<EMAIL>",
            "password": "securepass123",
            "phone": "+2347012345678",
            "first_name": "<PERSON>",
            "last_name": "<PERSON><PERSON>"
        }
        
        with patch('authentication.views.send_otp') as mock_send_otp:
            response = self.client.post(url, data, format="json")
            
            self.assertEqual(response.status_code, 201)
            self.assertTrue(User.objects.filter(email="<EMAIL>").exists())
            mock_send_otp.assert_called_once()
            
    def test_user_login(self):
        # Create a verified user
        user = User.objects.create_user(
            email="<EMAIL>", 
            password="testpass123",
            is_verified=True
        )
        
        url = reverse('login')
        data = {
            "email": "<EMAIL>",
            "password": "testpass123"
        }
        
        response = self.client.post(url, data, format="json")
        
        self.assertEqual(response.status_code, 200)
        self.assertIn("access", response.data)
        self.assertIn("refresh", response.data)
        
    def test_otp_verification(self):
        # Create user and OTP
        user = User.objects.create_user(
            email="<EMAIL>", 
            password="testpass123"
        )
        otp = OTP.objects.create(user=user, code="123456")
        
        url = reverse('verify-otp')
        data = {
            "email": "<EMAIL>",
            "otp": "123456"
        }
        
        response = self.client.post(url, data, format="json")
        
        self.assertEqual(response.status_code, 200)
        user.refresh_from_db()
        self.assertTrue(user.is_verified)