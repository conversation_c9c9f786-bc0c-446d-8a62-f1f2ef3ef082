import json
import uuid
import pytest
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from authentication.models import User, Driver
from trips.models import Trip
from unittest.mock import patch

pytestmark = pytest.mark.django_db

class TripAPITests(APITestCase):
    def setUp(self):
        self.user = User.objects.create_user(email="<EMAIL>", password="testpass123")
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
        
        self.driver = Driver.objects.create(
            user=User.objects.create_user(email="<EMAIL>", password="driverpass"),
            id=f"driver-{uuid.uuid4()}",
            latitude=123.456,
            longitude=789.012,
            vehicle_type="Bakkie",
            is_available=True
        )
        
    def test_find_drivers_view(self):
        url = reverse('find-drivers')
        data = {"pickup_lat": 123.456, "pickup_lon": 789.012, "vehicle_type": ["Bakkie"]}
        response = self.client.post(url, data, format="json")
        
        self.assertEqual(response.status_code, 200)
        self.assertIn("drivers", response.data)
        
    def test_calculate_fare_view(self):
        url = reverse('calculate-fare')
        data = {
            "pickup_lat": 123.456,
            "pickup_lon": 789.012,
            "dest_lat": 124.456,
            "dest_lon": 790.012,
            "vehicle_type": ["Bakkie"],
            "surge": False,
        }
        
        with patch("trips.views.get_route_data", return_value={"distance_km": 10.0, "duration_min": 20.0}):
            response = self.client.post(url, data, format="json")
            
            self.assertEqual(response.status_code, 200)
            self.assertIn("estimated_fare", response.data)
            self.assertEqual(response.data["estimated_fare"], 220.0)
            
    def test_update_trip_status_view(self):
        trip = Trip.objects.create(
            user=self.user,
            driver=self.driver,
            pickup="Pickup",
            destination="Destination",
            vehicle_type="Bakkie"
        )
        
        url = reverse('update-trip-status', args=[trip.id])
        data = {"trip_id": str(trip.id), "status": "picked up"}
        
        response = self.client.post(url, data, format="json")
        
        self.assertEqual(response.status_code, 200)
        trip.refresh_from_db()
        self.assertEqual(trip.status, "picked up")