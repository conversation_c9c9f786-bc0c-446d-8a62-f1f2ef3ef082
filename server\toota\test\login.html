<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RideShare - Login</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f7fa;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }
        .login-container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            padding: 30px;
            width: 350px;
        }
        .logo {
            text-align: center;
            margin-bottom: 25px;
        }
        .logo h1 {
            color: #4a6cf7;
            margin: 0;
        }
        h2 {
            color: #333;
            margin-top: 0;
            margin-bottom: 20px;
            text-align: center;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            color: #555;
            font-weight: 500;
        }
        input[type="email"],
        input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
            font-size: 16px;
        }
        button {
            background-color: #4a6cf7;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 12px 15px;
            width: 100%;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        button:hover {
            background-color: #3a5dd9;
        }
        .message {
            text-align: center;
            margin-top: 20px;
            color: #666;
        }
        .message a {
            color: #4a6cf7;
            text-decoration: none;
        }
        .message a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <h1>Toota</h1>
        </div>
        <h2>Welcome Back</h2>
        <form id="loginForm">
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" name="email" required>
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" required>
            </div>
            <button type="submit">Login</button>
        </form>
        <div class="message">
            Don't have an account? <a href="#">Sign up</a>
        </div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', function(event) {
            event.preventDefault();
    
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            console.log(password)
            const loginData = {
                email: email,
                password: password
            };
    
            fetch('http://localhost:8000/auth/login/user/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(loginData)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Login failed! Check your credentials.');
                }
                return response.json();
            })
            .then(data => {
                if (data.access) {
                    // Save token and email to localStorage
                    localStorage.setItem('authToken', data.access); // This is your access token!
                    localStorage.setItem('userEmail', email);
                    localStorage.setItem('isAuthenticated', 'true');
    
                    // Redirect to payment.html after successful login
                    window.location.href = 'payment.html';
                } else {
                    alert('Login failed! No token received.');
                }
            })
            .catch(error => {
                console.error('Login error:', error);
                alert(error.message || 'Something went wrong. Please try again.');
            });
        });
    </script>
    
</body>
</html>