import uuid
import pytest
from django.test import TestCase
from authentication.models import User, Driver
from trips.models import Trip, DriverRating

pytestmark = pytest.mark.django_db

class TripModelTests(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(email="<EMAIL>", password="testpass123")
        self.driver = Driver.objects.create(
            user=self.user, 
            id=f"driver-{uuid.uuid4()}", 
            latitude=123.456, 
            longitude=789.012,
            vehicle_type="Bakkie", 
            is_available=True
        )
        
    def test_trip_creation(self):
        trip = Trip.objects.create(
            user=self.user,
            pickup="Test Pickup",
            destination="Test Destination",
            vehicle_type="Bakkie",
            pickup_latitude=123.456,
            pickup_longitude=789.012,
            dest_latitude=124.456,
            dest_longitude=790.012,
            load_description="Test Load"
        )
        self.assertEqual(trip.status, "pending")
        self.assertIsNone(trip.driver)
        
    def test_trip_status_transitions(self):
        trip = Trip.objects.create(
            user=self.user,
            pickup="Test Pickup",
            destination="Test Destination",
            vehicle_type="Bakkie"
        )
        
        # Test status transitions
        trip.status = "accepted"
        trip.driver = self.driver
        trip.save()
        self.assertEqual(trip.status, "accepted")
        
        trip.status = "picked up"
        trip.save()
        self.assertEqual(trip.status, "picked up")
        
        trip.status = "completed"
        trip.save()
        self.assertEqual(trip.status, "completed")