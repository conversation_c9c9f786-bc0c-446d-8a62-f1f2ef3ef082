import json
import uuid
import pytest
import asyncio
from channels.testing import WebsocketCommunicator
from channels.db import database_sync_to_async
from django.test import Client
from authentication.models import User, Driver
from trips.models import Trip
from trips.consumers import TripRequestConsumer, DriverTripConsumer
from unittest.mock import patch

pytestmark = pytest.mark.django_db

@pytest.mark.asyncio
async def test_complete_trip_flow():
    """Test the entire trip flow from request to completion"""
    # Create passenger and driver
    passenger = await database_sync_to_async(User.objects.create_user)(
        email="<EMAIL>", password="pass123", is_verified=True
    )
    
    driver_user = await database_sync_to_async(User.objects.create_user)(
        email="<EMAIL>", password="pass123", is_verified=True
    )
    
    driver = await database_sync_to_async(Driver.objects.create)(
        user=driver_user, 
        id=f"driver-{uuid.uuid4()}", 
        latitude=123.456, 
        longitude=789.012,
        vehicle_type="Bakkie", 
        is_available=True
    )
    
    # 1. Passenger creates trip request
    passenger_communicator = WebsocketCommunicator(
        TripRequestConsumer.as_asgi(), f"/ws/trips/user/request/{driver.id}/"
    )
    passenger_communicator.scope["user"] = passenger
    passenger_communicator.scope["user"].id = passenger.id
    await passenger_communicator.connect()
    
    trip_data = {
        "action": "create_trip",
        "vehicle_type": "Bakkie",
        "pickup": "Test Pickup",
        "destination": "Test Destination",
        "pickup_lat": 123.456,
        "pickup_lon": 789.012,
        "dest_lat": 124.456,
        "dest_lon": 790.012,
        "surge": False,
        "load_description": "Test Load"
    }
    
    with patch("trips.consumers.get_route_data", return_value={"distance_km": 10.0, "duration_min": 20.0}):
        await passenger_communicator.send_json_to(trip_data)
        response = await passenger_communicator.receive_json_from()
        
        assert response["message"] == "Trip created successfully"
        trip_id = response["trip_id"]
    
    # 2. Driver receives and accepts trip
    driver_communicator = WebsocketCommunicator(
        DriverTripConsumer.as_asgi(), f"/ws/trips/driver/response/{driver.id}/"
    )
    driver_communicator.scope["user"] = driver_user
    await driver_communicator.connect()
    
    # Send driver acceptance
    driver_response = {
        "user_id": str(passenger.id),
        "trip_id": trip_id,
        "trip_response_status": "accepted"
    }
    
    await driver_communicator.send_json_to(driver_response)
    response = await driver_communicator.receive_json_from()
    
    assert response["type"] == "trip_accepted_confirmation"
    assert response["trip_id"] == trip_id
    
    # 3. Verify trip status updated in database
    trip = await database_sync_to_async(Trip.objects.get)(id=trip_id)
    assert trip.status == "accepted"
    assert str(trip.driver.id) == str(driver.id)
    
    # 4. Driver updates trip status to "arrived at pickup"
    status_update = {
        "trip_id": trip_id,
        "status": "arrived at pickup"
    }
    
    await driver_communicator.send_json_to(status_update)
    response = await driver_communicator.receive_json_from()
    
    assert response["status"] == "success"
    
    # 5. Driver updates trip status to "picked up"
    status_update = {
        "trip_id": trip_id,
        "status": "picked up"
    }
    
    await driver_communicator.send_json_to(status_update)
    response = await driver_communicator.receive_json_from()
    
    assert response["status"] == "success"
    
    # 6. Driver completes the trip
    status_update = {
        "trip_id": trip_id,
        "status": "completed"
    }
    
    await driver_communicator.send_json_to(status_update)
    response = await driver_communicator.receive_json_from()
    
    assert response["status"] == "success"
    
    # 7. Verify final trip status
    trip = await database_sync_to_async(Trip.objects.get)(id=trip_id)
    assert trip.status == "completed"
    
    # Clean up
    await passenger_communicator.disconnect()
    await driver_communicator.disconnect()