import json
import uuid
import pytest
from channels.testing import WebsocketCommunicator
from channels.db import database_sync_to_async
from django.test import Client
from authentication.models import User, Driver
from trips.models import Trip
from trips.consumers import (
    DriverLocationConsumer,
    PassengerGetLocationConsumer,
    TripRequestConsumer,
    DriverTripConsumer,
)
from unittest.mock import patch

pytestmark = pytest.mark.django_db

# Helper functions
async def create_user_async(email="<EMAIL>", password="testpass123"):
    return await database_sync_to_async(User.objects.create_user)(email=email, password=password)

async def create_driver_async(user=None, latitude=123.456, longitude=789.012, vehicle_type="Bakkie"):
    if not user:
        user = await create_user_async()
    return await database_sync_to_async(Driver.objects.create)(
        user=user, id=f"driver-{uuid.uuid4()}", latitude=latitude, longitude=longitude, 
        vehicle_type=vehicle_type, is_available=True
    )

@pytest.mark.asyncio
async def test_driver_location_updates():
    user = await create_user_async()
    driver = await create_driver_async(user=user)
    
    # Driver location consumer
    driver_communicator = WebsocketCommunicator(
        DriverLocationConsumer.as_asgi(), f"/ws/trips/driver/location/{driver.id}/"
    )
    driver_communicator.scope["user"] = user
    connected, _ = await driver_communicator.connect()
    assert connected
    
    # Passenger location consumer
    passenger = await create_user_async(email="<EMAIL>")
    passenger_communicator = WebsocketCommunicator(
        PassengerGetLocationConsumer.as_asgi(), f"/ws/trips/user/location/{driver.id}/"
    )
    passenger_communicator.scope["user"] = passenger
    connected, _ = await passenger_communicator.connect()
    assert connected
    
    # Driver sends location update
    await driver_communicator.send_json_to({"latitude": 123.457, "longitude": 789.013})
    
    # Driver should receive confirmation
    driver_response = await driver_communicator.receive_json_from()
    assert driver_response["latitude"] == 123.457
    assert driver_response["longitude"] == 789.013
    
    # Passenger should receive the update
    passenger_response = await passenger_communicator.receive_json_from()
    assert passenger_response["latitude"] == 123.457
    assert passenger_response["longitude"] == 789.013
    
    # Clean up
    await driver_communicator.disconnect()
    await passenger_communicator.disconnect()