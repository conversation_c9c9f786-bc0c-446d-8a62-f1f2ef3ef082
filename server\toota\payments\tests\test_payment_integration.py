import uuid
import pytest
from django.test import TestCase
from unittest.mock import patch, MagicMock
from authentication.models import User
from trips.models import Trip
from payments.models import Payment
from payments.services import FlutterwaveService

pytestmark = pytest.mark.django_db

class PaymentIntegrationTests(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(email="<EMAIL>", password="testpass123")
        self.trip = Trip.objects.create(
            user=self.user,
            pickup="Test Pickup",
            destination="Test Destination",
            vehicle_type="Bakkie",
            status="completed",
            fare=500.0
        )
        
    @patch('payments.services.requests.post')
    def test_flutterwave_payment_initiation(self, mock_post):
        # Mock the Flutterwave API response
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "status": "success",
            "message": "Payment link created",
            "data": {
                "link": "https://flutterwave.com/pay/12345abcde"
            }
        }
        mock_post.return_value = mock_response
        
        service = FlutterwaveService()
        result = service.initiate_payment(
            amount=self.trip.fare,
            email=self.user.email,
            reference=f"trip-{self.trip.id}",
            redirect_url="https://toota.com/payment-callback"
        )
        
        self.assertEqual(result["status"], "success")
        self.assertIn("link", result["data"])
        
    @patch('payments.services.requests.get')
    def test_flutterwave_payment_verification(self, mock_get):
        # Create a payment record
        payment = Payment.objects.create(
            user=self.user,
            trip=self.trip,
            amount=self.trip.fare,
            reference=f"trip-{self.trip.id}",
            status="pending"
        )
        
        # Mock the Flutterwave API verification response
        mock_response = MagicMock()
        mock_response.json.return_value = {
            "status": "success",
            "message": "Payment verified",
            "data": {
                "status": "successful",
                "amount": 500.0,
                "currency": "NGN",
                "tx_ref": f"trip-{self.trip.id}"
            }
        }
        mock_get.return_value = mock_response
        
        service = FlutterwaveService()
        result = service.verify_payment(payment.reference)
        
        self.assertEqual(result["status"], "success")
        self.assertEqual(result["data"]["status"], "successful")